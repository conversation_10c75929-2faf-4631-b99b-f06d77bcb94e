using Newtonsoft.Json;

namespace QuantConnect.Brokerages.Mexc.Messages {
  public class MexcTrade {
    [JsonProperty("p")]
    public decimal Price { get; set; }

    [JsonProperty("v")]
    public decimal Volume { get; set; }

    [JsonProperty("T")]
    public int Side { get; set; }

    [JsonProperty("O")]
    public int OpenPosition { get; set; }

    [JsonProperty("M")]
    public int SelfTransact { get; set; }

    [JsonProperty("t")]
    public long Timestamp { get; set; }
  }

  public class MexcTradeMessage: MexcBaseMessage {
    [JsonProperty("data")]
    public MexcTrade[] Data { get; set; }
  }

  public class MexcOrderDeal {
    [JsonProperty("category")]
    public int Category { get; set; }

    [JsonProperty("externalOid")]
    public string ExternalOid { get; set; }

    [JsonProperty("fee")]
    public decimal Fee { get; set; }

    [JsonProperty("feeCurrency")]
    public string FeeCurrency { get; set; }

    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("isSelf")]
    public bool IsSelf { get; set; }

    [JsonProperty("orderId")]
    public string OrderId { get; set; }

    [JsonProperty("positionMode")]
    public int PositionMode { get; set; }

    [JsonProperty("price")]
    public decimal Price { get; set; }

    [JsonProperty("profit")]
    public decimal Profit { get; set; }

    [JsonProperty("side")]
    public int Side { get; set; }

    [JsonProperty("symbol")]
    public string Symbol { get; set; }

    [JsonProperty("taker")]
    public bool Taker { get; set; }

    [JsonProperty("timestamp")]
    public long Timestamp { get; set; }

    [JsonProperty("vol")]
    public decimal Volume { get; set; }
  }

  // https://www.mexc.com/api-docs/futures/websocket-api#order
  public class MexcOrderDealMessage: MexcBaseMessage {
    [JsonProperty("data")]
    public MexcOrderDeal Data { get; set; }
  }

  public class MexcOrder {
    [JsonProperty("orderId")]
    public string OrderId { get; set; }

    [JsonProperty("symbol")]
    public string Symbol { get; set; }

    [JsonProperty("positionId")]
    public long PositionId { get; set; }

    [JsonProperty("price")]
    public decimal Price { get; set; }

    [JsonProperty("vol")]
    public decimal Volume { get; set; }

    [JsonProperty("leverage")]
    public int Leverage { get; set; }

    [JsonProperty("side")]
    public int Side { get; set; }

    [JsonProperty("category")]
    public int Category { get; set; }

    [JsonProperty("orderType")]
    public int OrderType { get; set; }

    [JsonProperty("dealVol")]
    public decimal DealVolume { get; set; }

    [JsonProperty("dealAvgPrice")]
    public decimal DealAveragePrice { get; set; }

    [JsonProperty("state")]
    public int State { get; set; }

    [JsonProperty("createTime")]
    public long CreateTime { get; set; }

    [JsonProperty("updateTime")]
    public long UpdateTime { get; set; }

    [JsonProperty("externalOid")]
    public string ExternalOid { get; set; }

    [JsonProperty("remainVol")]
    public decimal RemainVolume { get; set; }

    /*
      0:normal
      1:param_invalid
      2:insufficient_balance
      3:position_not_exists
      4:position_not_enough
      5:position_liq
      6:order_liq
      7:risk_level_limit
      8:sys_cancel
      9:position_mode_not_match
      10:reduce_only_liq
      11:contract_not_enable
      12:delivery_cancel
      13:position_liq_cancel
      14:adl_cancel
      15:black_user_cancel
      16:settle_funding_cancel
      17:position_im_change_cancel
      18:ioc_cancel
      19:fok_cancel
      20:post_only_cancel
      21:market_cancel
    */
    [JsonProperty("errorCode")]
    public int ErrorCode { get; set; }

    [JsonProperty("usedMargin")]
    public decimal UsedMargin { get; set; }

    [JsonProperty("takerFeeRate")]
    public decimal TakerFeeRate { get; set; }

    [JsonProperty("makerFeeRate")]
    public decimal MakerFeeRate { get; set; }

    [JsonProperty("reduceOnly")]
    public bool ReduceOnly { get; set; }
  }

  public class MexcOrderResponse {
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("data")]
    public MexcOrderResult Data { get; set; }
  }

  public class MexcOrderResult {
    [JsonProperty("orderId")]
    public string OrderId { get; set; }
  }

  public class MexcOrderListResponse {
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("data")]
    public MexcOrder[] Data { get; set; }
  }

  public class MexcPlanOrder {
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("orderId")]
    public string OrderId { get; set; }

    [JsonProperty("symbol")]
    public string Symbol { get; set; }

    [JsonProperty("leverage")]
    public int Leverage { get; set; }

    [JsonProperty("side")]
    public int Side { get; set; }

    [JsonProperty("vol")]
    public decimal Volume { get; set; }

    [JsonProperty("openType")]
    public int OpenType { get; set; }

    [JsonProperty("orderType")]
    public int OrderType { get; set; }

    [JsonProperty("price")]
    public decimal Price { get; set; }

    [JsonProperty("triggerPrice")]
    public decimal TriggerPrice { get; set; }

    [JsonProperty("triggerType")]
    public int TriggerType { get; set; }

    [JsonProperty("executeCycle")]
    public int ExecuteCycle { get; set; }

    [JsonProperty("trend")]
    public int Trend { get; set; }

    [JsonProperty("state")]
    public int State { get; set; }

    [JsonProperty("createTime")]
    public long CreateTime { get; set; }

    [JsonProperty("updateTime")]
    public long UpdateTime { get; set; }

    [JsonProperty("positionMode")]
    public int PositionMode { get; set; }

    [JsonProperty("reduceOnly")]
    public bool ReduceOnly { get; set; }

    [JsonProperty("lossTrend")]
    public int LossTrend { get; set; }

    [JsonProperty("profitTrend")]
    public int ProfitTrend { get; set; }
  }

  public class MexcPlanOrderMessage: MexcBaseMessage {
    [JsonProperty("data")]
    public MexcPlanOrder Data { get; set; }
  }

  public class MexcOrderMessage: MexcBaseMessage {
    [JsonProperty("data")]
    public MexcOrder Data { get; set; }
  }

  public class MexcPlanOrderResponse {
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("data")]
    public string Data { get; set; }
  }

  public class MexcPlanOrderListResponse {
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("data")]
    public MexcPlanOrder[] Data { get; set; }
  }

  public class MexcTicker {
    [JsonProperty("symbol")]
    public string Symbol { get; set; }

    [JsonProperty("lastPrice")]
    public decimal LastPrice { get; set; }

    [JsonProperty("riseFallRate")]
    public decimal RiseFallRate { get; set; }

    [JsonProperty("fairPrice")]
    public decimal FairPrice { get; set; }

    [JsonProperty("indexPrice")]
    public decimal IndexPrice { get; set; }

    [JsonProperty("volume24")]
    public decimal Volume24 { get; set; }

    [JsonProperty("amount24")]
    public decimal Amount24 { get; set; }

    [JsonProperty("maxBidPrice")]
    public decimal MaxBidPrice { get; set; }

    [JsonProperty("minAskPrice")]
    public decimal MinAskPrice { get; set; }

    [JsonProperty("lower24Price")]
    public decimal Lower24Price { get; set; }

    [JsonProperty("high24Price")]
    public decimal High24Price { get; set; }

    [JsonProperty("timestamp")]
    public long Timestamp { get; set; }

    [JsonProperty("bid1")]
    public decimal Bid1 { get; set; }

    [JsonProperty("ask1")]
    public decimal Ask1 { get; set; }

    [JsonProperty("holdVol")]
    public decimal HoldVol { get; set; }

    [JsonProperty("riseFallValue")]
    public decimal RiseFallValue { get; set; }

    [JsonProperty("fundingRate")]
    public decimal FundingRate { get; set; }
  }

  public class MexcTickerMessage: MexcBaseMessage {
    [JsonProperty("data")]
    public MexcTicker Data { get; set; }
  }

  public class MexcPersonalAsset {
    [JsonProperty("availableBalance")]
    public decimal AvailableBalance { get; set; }

    [JsonProperty("bonus")]
    public decimal Bonus { get; set; }

    [JsonProperty("currency")]
    public string Currency { get; set; }

    [JsonProperty("frozenBalance")]
    public decimal FrozenBalance { get; set; }

    [JsonProperty("positionMargin")]
    public decimal PositionMargin { get; set; }
  }

  public class MexcPersonalAssetMessage: MexcBaseMessage {
    [JsonProperty("data")]
    public MexcPersonalAsset Data { get; set; }
  }

  public class MexcPersonalPosition {
    [JsonProperty("positionId")]
    public long PositionId { get; set; }

    [JsonProperty("symbol")]
    public string Symbol { get; set; }

    [JsonProperty("positionType")]
    public int PositionType { get; set; }

    [JsonProperty("openType")]
    public int OpenType { get; set; }

    [JsonProperty("state")]
    public int State { get; set; }

    [JsonProperty("holdVol")]
    public decimal HoldVol { get; set; }

    [JsonProperty("frozenVol")]
    public decimal FrozenVol { get; set; }

    [JsonProperty("closeVol")]
    public decimal CloseVol { get; set; }

    [JsonProperty("holdAvgPrice")]
    public decimal HoldAvgPrice { get; set; }

    [JsonProperty("holdAvgPriceFullyScale")]
    public string HoldAvgPriceFullyScale { get; set; }

    [JsonProperty("openAvgPrice")]
    public decimal OpenAvgPrice { get; set; }

    [JsonProperty("openAvgPriceFullyScale")]
    public string OpenAvgPriceFullyScale { get; set; }

    [JsonProperty("closeAvgPrice")]
    public decimal CloseAvgPrice { get; set; }

    [JsonProperty("liquidatePrice")]
    public decimal LiquidatePrice { get; set; }

    [JsonProperty("oim")]
    public decimal Oim { get; set; }

    [JsonProperty("im")]
    public decimal Im { get; set; }

    [JsonProperty("holdFee")]
    public decimal HoldFee { get; set; }

    [JsonProperty("realised")]
    public decimal Realised { get; set; }

    [JsonProperty("leverage")]
    public int Leverage { get; set; }

    [JsonProperty("marginRatio")]
    public decimal MarginRatio { get; set; }

    [JsonProperty("createTime")]
    public long CreateTime { get; set; }

    [JsonProperty("updateTime")]
    public long UpdateTime { get; set; }

    [JsonProperty("autoAddIm")]
    public bool AutoAddIm { get; set; }

    [JsonProperty("version")]
    public int Version { get; set; }

    [JsonProperty("pnl")]
    public decimal Pnl { get; set; }

    [JsonProperty("newOpenAvgPrice")]
    public decimal NewOpenAvgPrice { get; set; }

    [JsonProperty("newCloseAvgPrice")]
    public decimal NewCloseAvgPrice { get; set; }

    [JsonProperty("closeProfitLoss")]
    public decimal CloseProfitLoss { get; set; }

    [JsonProperty("fee")]
    public decimal Fee { get; set; }

    [JsonProperty("makerFeeRate")]
    public decimal MakerFeeRate { get; set; }

    [JsonProperty("takerFeeRate")]
    public decimal TakerFeeRate { get; set; }

    [JsonProperty("adlLevel")]
    public int AdlLevel { get; set; }
  }

  public class MexcPersonalPositionMessage: MexcBaseMessage {
    [JsonProperty("data")]
    public MexcPersonalPosition Data { get; set; }
  }
}
